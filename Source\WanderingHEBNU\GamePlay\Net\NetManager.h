#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "UDP/UDPSocketComponent.h"
#include "TCP/TCPSocketComponent.h"
#include "Payload.h"
#include "NetManager.generated.h"

class ALetterObject;
class UMainGameInstance;
class AGuest<PERSON>haracter;
class <PERSON>ainController;
class <PERSON>ainCharacter;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnLoginResultDelegate, bool, bSuccess, const FString&, Message);

UENUM(BlueprintType)
enum class ENetType : uint8
{
	None = 0 UMETA(DisplayName = "无"),
	UDP = 1 UMETA(DisplayName = "UDP"),
	TCP = 2 UMETA(DisplayName = "TCP"),
};

UCLASS()
class ANetManager : public AActor
{
	GENERATED_BODY()

public:
	ANetManager();

	virtual void BeginPlay() override;

	virtual void BeginDestroy() override;

	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

	virtual void Tick(float DeltaTime) override;

	int GetUserID() const { return UserID; }

	void Start();

	void AddGuest(int ID, AGuestCharacter* Guest);

	AGuestCharacter* GetGuest(const int ID);

	void SendStream(const NetPacket::FStream& Stream, const ENetType Type) const;

	void SendPong(ENetType Type) const;

	void SendLogin();

	void SendLogout(ENetType Type);

	void SendLogoutAll();

	void SendMotion();
	
	void CheckDailyLetterLimit();

	void SendLetterData(NetPacket::FLetterData& Letter) const;

	void RequestLetterObjects() const;

	void SendLoginRequest(NetPacket::FLoginPayload& Login) const;

	bool GetLoginResult() const {return LoginResult;};
	
	UPROPERTY(BlueprintAssignable, Category = "登录事件")
	FOnLoginResultDelegate OnLoginResult;

protected:
	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "用户ID")
	int UserID=-1;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "登录状态")
	bool LoginResult=false;

	NetPacket::FGuestMotion LastMotionRep;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "玩家Map")
	TMap<int, AGuestCharacter*> GuestMap;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "本地玩家控制器")
	TObjectPtr<AMainController> LocalController;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "本地玩家")
	TObjectPtr<AMainCharacter> LocalCharacter;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "游戏实例")
	TObjectPtr<UMainGameInstance> MainGameInstance = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "网络设置", DisplayName = "玩家类模版")
	TSubclassOf<AGuestCharacter> GuestSubclassOf;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "网络设置", DisplayName = "UDP套接字组件")
	TObjectPtr<UUDPSocketComponent> UDPSocketComponent;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "网络设置", DisplayName = "TCP套接字组件")
	TObjectPtr<UTCPSocketComponent> TCPSocketComponent;

	UFUNCTION()
	void OnUDPSocketStart(const FString& BoundIP, int BoundPort);

	UFUNCTION()
	void OnUDPSocketClose();

	UFUNCTION()
	void OnUDPReceivedMessage(const FString& Message, const FString& Endpoint, const int Port);

	UFUNCTION()
	void OnUDPReceivedMessageInGameThread(const FString& Message, const FString& Endpoint, const int Port);

	UFUNCTION()
	void OnTCPSocketStart(const FString& BoundIP, int BoundPort);

	UFUNCTION()
	void OnTCPSocketClose();

	UFUNCTION()
	void OnTCPReceivedMessage(const FString& Message, const FString& Endpoint, const int Port);

	void OnLetterDataReceived(const NetPacket::FLetterData& LetterData) const;
	
	UFUNCTION()
	void OnTCPReceivedMessageInGameThread(const FString& Message, const FString& Endpoint, const int Port);
	
	void OnGuestLogin(int GuestID, const NetPacket::FGuestLogin& Value);

	void OnGuestLogout(int GuestID);

	void OnGuestMotion(int GuestID, const NetPacket::FGuestMotion& Value);

	TArray<TSubclassOf<ALetterObject>> LettersSubclassOf;


};
