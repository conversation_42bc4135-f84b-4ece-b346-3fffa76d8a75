#pragma once

#include "CoreMinimal.h"

namespace NetPacket
{
	inline FString JsonSerialize(const TSharedPtr<FJsonObject>& Json)
	{
		if (!Json . IsValid() || Json -> Values . Num() <= 0) { return FString(); }
		FString Str;
		const TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> Writer = TJsonWriterFactory<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>::Create(&Str, 0);
		FJsonSerializer::Serialize(Json . ToSharedRef(), Writer);
		return Str;
	}

	inline TSharedPtr<FJsonObject> JsonDeserialize(const FString& Str)
	{
		TSharedPtr<FJsonObject> Json = MakeShareable(new FJsonObject);
		const TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Str);
		FJsonSerializer::Deserialize(Reader, Json);
		return Json;
	}

	inline double JsonGetNumber(const TSharedPtr<FJsonObject>& Json, const FString& Field)
	{
		return Json -> HasField(Field) ? Json -> GetNumberField(Field) : 0;
	}

	inline FString JsonGetString(const TSharedPtr<FJsonObject>& Json, const FString& Field)
	{
		return Json -> HasField(Field) ? Json -> GetStringField(Field) : FString();
	}

	inline bool JsonGetBool(const TSharedPtr<FJsonObject>& Json, const FString& Field)
	{
		return Json -> HasField(Field) ? Json -> GetBoolField(Field) : false;
	}

	inline TArray<TSharedPtr<FJsonValue>> JsonGetArray(const TSharedPtr<FJsonObject>& Json, const FString& Field)
	{
		return Json -> HasField(Field) ? Json -> GetArrayField(Field) : TArray<TSharedPtr<FJsonValue>>();
	}

	inline TSharedPtr<FJsonObject> JsonGetObject(const TSharedPtr<FJsonObject>& Json, const FString& Field)
	{
		return Json -> HasField(Field) ? Json -> GetObjectField(Field) : nullptr;
	}

	class FPayloadVector
	{
	public:
		FPayloadVector() = default;

		explicit FPayloadVector(const FVector& Value) : Vector(Value) { return; }

		explicit FPayloadVector(const FString& Value)
		{
			TArray<FString> Array;
			Value . ParseIntoArray(Array, TEXT(":"), true);
			if (Array . Num() < 3) { return; }
			Vector . X = FCString::Atof(*Array[0]);
			Vector . Y = FCString::Atof(*Array[1]);
			Vector . Z = FCString::Atof(*Array[2]);
		}

		FString Serialize() const
		{
			return FString::Printf(TEXT("%.2f:%.2f:%.2f"), Vector . X, Vector . Y, Vector . Z);
		}

		FVector& GetVector() { return Vector; }

		FVector GetVector() const { return Vector; }

	protected:
		FVector Vector = FVector::ZeroVector;
	};

	class FPayloadRotation
	{
	public:
		FPayloadRotation() = default;

		explicit FPayloadRotation(const FRotator& Value) : Rotation(Value) { return; }

		explicit FPayloadRotation(const FString& Value)
		{
			TArray<FString> Array;
			Value . ParseIntoArray(Array, TEXT(":"), true);
			if (Array . Num() < 3) { return; }
			Rotation . Pitch = FCString::Atof(*Array[0]);
			Rotation . Yaw = FCString::Atof(*Array[1]);
			Rotation . Roll = FCString::Atof(*Array[2]);
		}

		FString Serialize() const
		{
			return FString::Printf(TEXT("%.2f:%.2f:%.2f"), Rotation . Pitch, Rotation . Yaw, Rotation . Roll);
		}

		FRotator& GetRotation() { return Rotation; }

		FRotator GetRotation() const { return Rotation; }

	protected:
		FRotator Rotation = FRotator::ZeroRotator;
	};

	class FPayloadTransform
	{
	public:
		FPayloadTransform() = default;

		explicit FPayloadTransform(const FTransform& Value) : Transform(Value) { return; }

		explicit FPayloadTransform(const FString& Value)
		{
			TArray<FString> Array;
			Value . ParseIntoArray(Array, TEXT("|"), true);
			if (Array . Num() < 3) { return; }
			Transform . SetLocation(FPayloadVector(Array[0]) . GetVector());
			Transform . SetRotation(FPayloadRotation(Array[1]) . GetRotation() . Quaternion());
			Transform . SetScale3D(FPayloadVector(Array[2]) . GetVector());
		}

		FString Serialize() const
		{
			const FVector Location = Transform . GetLocation();
			const FRotator Rotation = Transform . GetRotation() . Rotator();
			const FVector Scale = Transform . GetScale3D();
			return FString::Printf(
				TEXT("%.2f:%.2f:%.2f|%.2f:%.2f:%.2f|%.2f:%.2f:%.2f"),
				Location . X, Location . Y, Location . Z,
				Rotation . Pitch, Rotation . Yaw, Rotation . Roll,
				Scale . X, Scale . Y, Scale . Z
			);
		}

		FTransform& GetTransform() { return Transform; }

		FTransform GetTransform() const { return Transform; }

	protected:
		FTransform Transform = FTransform();
	};

	class FPayload
	{
	public:
		FPayload() { Json = MakeShareable(new FJsonObject); }

		virtual ~FPayload() = default;

		virtual TSharedPtr<FJsonObject> Serialize() { return nullptr; }

		virtual void Deserialize(const TSharedPtr<FJsonObject>& InJson) { return; }

		virtual FString ToString() { return JsonSerialize(Json); }

		TSharedPtr<FJsonObject> GetJson() const { return Json; }

	protected:
		TSharedPtr<FJsonObject> Json;
	};

	/**
	 * 用于最终的数据传输
	 */
	class FStream final : public FPayload
	{
	public:
		FStream() = default;

		explicit FStream(const FString& Raw)
		{
			const TSharedPtr<FJsonObject> InJson = JsonDeserialize(Raw);
			Deserialize(InJson);
		}

		explicit FStream(const TSharedPtr<FJsonObject>& InJson)
		{
			Deserialize(InJson);
		}

		void SetUserID(const int Value) { UserID = Value; }

		int GetUserID() const { return UserID; }

		void SetType(const FString& Value) { Type = Value; }

		FString GetType() const { return Type; }

		void SetPayload(const TSharedPtr<FJsonObject>& Value) { Payload = Value; }

		TSharedPtr<FJsonObject> GetPayload() const { return Payload; }

		virtual TSharedPtr<FJsonObject> Serialize() override
		{
			Json -> SetNumberField("UserID", UserID);
			Json -> SetStringField("Type", Type);
			Json -> SetObjectField("Payload", Payload);
			return Json;
		}

		virtual void Deserialize(const TSharedPtr<FJsonObject>& InJson) override
		{
			UserID = JsonGetNumber(InJson, "UserID");
			Type = JsonGetString(InJson, "Type");
			Payload = JsonGetObject(InJson, "Payload");
		}

	protected:
		int UserID = -1;

		FString Type = FString();

		TSharedPtr<FJsonObject> Payload = nullptr;
	};

	class FGuestLogin final : public FPayload
	{
	public:
		FGuestLogin() = default;

		explicit FGuestLogin(const FString& Raw)
		{
			const TSharedPtr<FJsonObject> InJson = JsonDeserialize(Raw);
			Deserialize(InJson);
		}

		explicit FGuestLogin(const TSharedPtr<FJsonObject>& InJson)
		{
			Deserialize(InJson);
		}
		
		void SetUsername(const int& Value) { Username = Value; }
		int GetUsername() const { return Username; }
		
		void SetPassword(const FString& Value) { Password = Value; }
		FString GetPassword() const { return Password; }

		void SetSpawnAt(const FTransform& Value) { SpawnAt = FPayloadTransform(Value); }
		FTransform GetSpawnAt() const { return SpawnAt . GetTransform(); }

		virtual TSharedPtr<FJsonObject> Serialize() override
		{
			//Json->SetStringField("UserName", Username);
			Json->SetStringField("Password", Password);
			Json -> SetStringField("SpawnAt", SpawnAt . Serialize());
			return Json;
		}

		virtual void Deserialize(const TSharedPtr<FJsonObject>& InJson) override
		{
			SpawnAt = FPayloadTransform(JsonGetString(InJson, "SpawnAt"));
			Password = JsonGetString(InJson, "Password");
			//Username = JsonGetNumber(InJson, "Username");
		}

	protected:
		FPayloadTransform SpawnAt = FPayloadTransform();
		int Username;
		FString Password;
	};

	class FGuestMotion final : public FPayload
	{
	public:
		FGuestMotion() = default;

		explicit FGuestMotion(const FString& Raw)
		{
			const TSharedPtr<FJsonObject> InJson = JsonDeserialize(Raw);
			Deserialize(InJson);
		}

		explicit FGuestMotion(const TSharedPtr<FJsonObject>& InJson)
		{
			Deserialize(InJson);
		}

		void SetIsInAir(const bool Value) { bIsInAir = Value; }

		bool IsInAir() const { return bIsInAir; }

		void SetIsWaving(const bool Value) { bIsWaving = Value; }

		bool IsWaving() const { return bIsWaving; }

		void SetMotion(const FTransform& Value) { Motion = FPayloadTransform(Value); }

		FTransform GetMotion() const { return Motion . GetTransform(); }

		virtual TSharedPtr<FJsonObject> Serialize() override
		{
			Json -> SetBoolField("IsInAir", bIsInAir);
			Json -> SetBoolField("IsWaving", bIsWaving);
			Json -> SetStringField("Motion", Motion . Serialize());
			return Json;
		}

		virtual void Deserialize(const TSharedPtr<FJsonObject>& InJson) override
		{
			bIsInAir = JsonGetBool(InJson, "IsInAir");
			bIsWaving = JsonGetBool(InJson, "IsWaving");
			Motion = FPayloadTransform(JsonGetString(InJson, "Motion"));
		}

		bool Equals(const FGuestMotion& Other) const
		{
			return bIsInAir == Other . IsInAir() &&
				bIsWaving == Other . IsWaving() &&
				GetMotion() . Equals(Other . GetMotion());
		}

	protected:
		bool bIsInAir = false;

		bool bIsWaving = false;

		FPayloadTransform Motion = FPayloadTransform();
	};

	class FLetterData final : public FPayload
	{
	public:
		FLetterData() = default;

		explicit FLetterData(const FString& Raw)
		{
			const TSharedPtr<FJsonObject> InJson = JsonDeserialize(Raw);
			Deserialize(InJson);
		}

		explicit FLetterData(const TSharedPtr<FJsonObject>& InJson)
		{
			Deserialize(InJson);
		}

		void SetContent(const FString& Value) { Content = Value; }
		FString GetContent() const { return Content; }

		void SetStyleIndex(const int Value) { StyleIndex = Value; }
		int GetStyleIndex() const { return StyleIndex; }

		void SetLocation(const FVector& Value) { Location = FPayloadVector(Value); }
		FVector GetLocation() const { return Location . GetVector(); }

		void SetRotation(const FRotator& Value) { Rotation = FPayloadRotation(Value); }
		FRotator GetRotation() const { return Rotation . GetRotation(); }

		virtual TSharedPtr<FJsonObject> Serialize() override
		{
			Json -> SetStringField("Content", Content);
			Json -> SetNumberField("StyleIndex", StyleIndex);
			Json -> SetStringField("Location", Location . Serialize());
			Json -> SetStringField("Rotation", Rotation . Serialize());
			return Json;
		}

		virtual void Deserialize(const TSharedPtr<FJsonObject>& InJson) override
		{
			Content = JsonGetString(InJson, "Content");
			StyleIndex = JsonGetNumber(InJson, "StyleIndex");
			Location = FPayloadVector(JsonGetString(InJson, "Location"));
			Rotation = FPayloadRotation(JsonGetString(InJson, "Rotation"));
		}

	protected:
		FString Content;
		int StyleIndex = -1;
		FPayloadVector Location;
		FPayloadRotation Rotation;
	};

	class FLoginPayload final: public FPayload
	{
	public:
		FLoginPayload() = default;

		explicit FLoginPayload(const FString& Raw)
		{
			const TSharedPtr<FJsonObject> InJson = JsonDeserialize(Raw);
			Deserialize(InJson);
		}

		explicit FLoginPayload(const TSharedPtr<FJsonObject>& InJson)
		{
			Deserialize(InJson);
		}

		

		virtual TSharedPtr<FJsonObject> Serialize() override
		{
			
			return Json;
		}

		virtual void Deserialize(const TSharedPtr<FJsonObject>& InJson) override
		{
			
		}

	
	};
	
}
